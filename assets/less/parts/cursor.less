// out: false
@import '../vw_values.less';
@import '../constants.less';

.mainCursor {
    position: fixed;
    width: 60vw;
    height: 60vw;
    background: @secondaryColorLight;
    pointer-events: none;
    opacity: 0;
    .filter(blur(@vw50));
    transform: translate(-50%, -50%);
    pointer-events: none;
    .rounded(50%);
    left: -30vw;
    top: -30vw;
    -webkit-mask-image: radial-gradient(rgba(0,0,0,1), rgba(0,0,0,0), rgba(0,0,0,0));
    mask-image: radial-gradient(rgba(0,0,0,1), rgba(0,0,0,0), rgba(0,0,0,0));
    transition: opacity 0.3s ease-out;
    &.show {
        opacity: .6;
    }
}