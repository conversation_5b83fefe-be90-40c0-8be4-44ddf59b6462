<?php
  $video = "";
  $size = 'full'; 
  $video = get_field("video");
  $image = get_field("image");
  $buttons = get_field('buttons');
?>
<section class="aboutBlock primary" data-hide-cursor data-init <?php if (get_field("anchor")){ echo 'data-anchor="' . esc_attr(get_field("anchor")) . '"'; } ?>>
      <div class="light" data-parallax-speed="10"><div class="innerLight"></div></div>
      <div class="contentWrapper">
        <div class="col small">
            <?php if (get_field("subtitle")){ ?><h2 class="subTitle white"><?php the_field("subtitle"); ?></h2><?php } ?>
        </div>
        <div class="col">
            <div class="innerCol">
                <?php if (get_field("text")){ ?>
                    <div class="introTextWrapper" data-anim-text>
                        <div class="normalTitle white" data-lines><?php the_field("text"); ?></div>
                        <div class="normalTitle white overlayText" data-lines aria-hidden="true"><?php the_field("text"); ?></div>
                    </div>
                <?php } ?>
                <div class="text white">
                  <?php the_field("extra_text"); ?>
                </div>
                <div class="buttonWrapper">
                  <?php if ($buttons): ?>
                    <?php foreach ($buttons as $button): ?>
                      <?php if (!empty($button['button'])): ?>
                        <?php render_button_from_array($button['button']); ?>
                      <?php endif; ?>
                    <?php endforeach; ?>
                  <?php endif; ?>
              </div>
            </div>
        </div>
        <div class="col">
            <div class="imageWrapper">
                <div class="innerImage">
                    <?php if ($video): ?>
                        <video data-parallax data-parallax-speed="2" poster="<?php echo esc_url($image['url']); ?>" class="video" muted playsinline loop autoplay>
                            <source src="<?php echo esc_url($video); ?>" type="video/mp4">
                        </video>
                    <?php elseif ($image): ?>
                        <img data-parallax data-parallax-speed="2" class="lazy" data-src="<?php echo esc_url($image["sizes"]['full']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>
