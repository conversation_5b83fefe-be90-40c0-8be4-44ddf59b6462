// out: ../css/home-header-block.css, compress: true, strictMath: true
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.homeHeaderBlock {
  padding-top: (@vw100 * 5) + @vw40 !important;
  text-align: left;
  color: @hardWhite;
  background: @primaryColor;
  position: relative;
  overflow: hidden;
  &.inview {
    .titleWrapper {
      .buttonWrapper {
        .transform(translateY(0) skewX(0));
        opacity: 1;
      }
      .sliderIndicatorWrapper, .navigation {
        opacity: 1;
        .transitionMore(opacity, 0.45s, 0.45s);
      }
    }
    .marqueeWrapper {
      opacity: 1;
      .transform(translateY(0));
      transition: opacity 0.6s 0.45s ease-in-out, transform 0.6s 0.45s ease-in-out;
    }
  }
  .backgroundVideo {
    position: absolute;
    top: -10%;
    width: 100%;
    height: 120%;
    left: 0;
    &:before {
      height: 100%;
      position: absolute;
      bottom: 0;
      z-index: 1;
      width: 100%;
      left: 0;
      content: '';
      background: @primaryColor;
      opacity: .4;
    }
    &:after {
      height: 100%;
      position: absolute;
      bottom: 0;
      width: 100%;
      left: 0;
      z-index: 2;
      content: '';
      background: (linear-gradient(rgba(8,0,54,0), @primaryColor));
    }
    img, video {
      opacity: .65;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: top;
    }
  }
  svg {
    width: (@vw106 * 6) + (@vw16 * 5);
    height: auto;
    position: absolute;
    opacity: .5;
    mix-blend-mode: soft-light;
    top: 50%;
    left: 50%;
    .transform(translate(-50%,-50%));
    path, rect {
      fill: @hardWhite;
    }
  }
  .artistSlider {
    text-align: left;
    display: block;
    margin: auto;
    left: 0;
    right: 0;
    height: auto;
    bottom: 0;
    position: absolute;
    width: 100%;
    height: calc(100% ~"-" @vw100 ~"-" @vw13);
    &:after {
      height: 80%;
      position: absolute;
      bottom: 0;
      width: 100%;
      left: 0;
      z-index: 2;
      content: '';
      background: (linear-gradient(rgba(8,0,54,0), @hardBlack));
    }
    .imageSlider {
      position: absolute;
      display: block;
      bottom: 0;
      margin: auto;
      margin-bottom: 0;
      width: 100%;
      height: 100%;
      .slide {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        opacity: 0;
        height: 100%;
        .transitionMore(opacity, .45s, 0s, ease-in-out);
        &.active { 
          opacity: 1;
          z-index: 2;
         .transitionMore(opacity, .45s, .45s, ease-in-out);
          img {
            .transform(scale(1));
            .transitionMore(transform, 0.9s, .45s, cubic-bezier(0.85, 0, 0.15, 1));
          }
        }
        img {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          object-fit: contain;
          -webkit-filter: drop-shadow(0 @vw200 @vw100 rgba(255,255,255,.3));
          object-position: top;
          .transitionMore(transform, 0.6s, .15s, cubic-bezier(0.85, 0, 0.15, 1));
          .transform(scale(.9));
        }
      }
    }
  }
  .backgroundWrapper { 
    width: 80vw;
    position: absolute;
    pointer-events: none; 
    z-index: -1;
    height: 80vw; 
    .transform(translateY(-50%));
    top: 50%;
    left: @vw100;
    .background {
      opacity: .4;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      .rounded(50%);
      height: 100%; 
      background: @primaryColor;
      -webkit-mask-image: radial-gradient(rgba(0,0,0,1), rgba(0,0,0,0), rgba(0,0,0,0));
      mask-image: radial-gradient(rgba(0,0,0,1), rgba(0,0,0,0), rgba(0,0,0,0));
    }
  }
  .titleSlider {
      color: @hardWhite;
      position: relative;
      width: 100%;
      text-transform: uppercase;
      display: block;
      .inlineCol, .divider {
        display: inline-block;
        width: auto;
      }
      .textLink {
        display: table;
      }
      .titleSlide {
        position: absolute;
        top: 0;
        left: 0;
        -webkit-filter: blur(@vw10);
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.3s 0.03s ease-in-out, -webkit-filter 0.3s 0s ease-in-out;
        &.active {
          -webkit-filter: blur(0);
          opacity: 1;
          pointer-events: all;
          transition: opacity 0.3s 0.33s ease-in-out, -webkit-filter 0.6s 0.3s ease-in-out;
        }
      }
    }
    .sliderIndicatorWrapper, .navigation {
      opacity: 0;
    }
      .sliderIndicatorWrapper {
        display: flex;
        align-items: center;
        margin-bottom: 1.5em;
        .sliderIndicatorCount {
          font-size: 1.1em;
          font-weight: 700;
          letter-spacing: 0.05em;
          color: @hardWhite;
          margin-right: 1.5em;
          min-width: 3em;
          text-align: left;
        }
    }
    .sliderIndicatorBar {
        flex: 1;
        height: 1px;
        background: rgba(255,255,255,0.4);
        overflow: hidden;
        position: relative;
        .sliderIndicatorProgress {
          height: 100%;
          width: 100%;
          background: @hardWhite;
          .transform(translate3d(0px, 0px, 0px) scale(0.8842, 1));
          transform-origin: left center;
          transition: transform 0.3s linear;
          will-change: transform;
        }
      }
    .navigation {
      display: block;
      margin-top: @vw14;
    }
  .titleWrapper {
    position: relative;
    width: 100%;
    z-index: 6;
    .bigTitle {
      margin: @vw18 0 @vw44 0;
    }
    .innerCol {
      display: inline-block; 
      width: 66.6666%;
      vertical-align: top;
      &.small {
        width: 33.3333%;
        padding-right: @vw106 + @vw16;
      }
      .arrowButton {
        &:not(:last-child) {
          margin-right: @vw16;
        }
      }
    }
    .buttonWrapper {
      display: inline-block;
      vertical-align: middle;
      margin-right: @vw16;
      .transform(translateY(@vw20) skewX(10deg));
      opacity: 0;
      transition: opacity 0.45s 0.9s ease-in-out, transform 0.45s .9s ease-in-out;
      .button {
        &:not(:last-child) {
          margin-right: @vw16;
        }
      }
    }
  }
  .imageWrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    -webkit-filter: blur(0px);
    transition: opacity 0.3s 0.03s ease-in-out, -webkit-filter 0.6s 0s ease-in-out;
    &.invisible {
      opacity: 0;
      -webkit-filter: blur(@vw40);
    }
    canvas {
      position: absolute;
      top: 0;
      left: 0;
      width: 100% !important;
      height: 100% !important;
      object-fit: contain !important;
      object-position: center;
    }
    img {
      display: none;
    }
  }
  .buttons {
    margin-top: @vw50;
    display: block;
    text-align: left;
    .button {
      &:not(:last-child) {
        margin-right: @vw20;
      }
    }
  }
}

.bottomImageWrapper {
  position: absolute;
  pointer-events: none;
  bottom: 0;
  width: 100%;
  height: auto;
  .transform(translate3d(0,0,0));
  .bottomImage {
    position: relative;
    bottom: 0;
    width: 100%;
    height: auto;
    object-fit: contain;
  }
}

.imageSlide {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  img {
    object-position: top;
  }
}
.slide {
  position: relative;
  width: 100%;
  height: 100%;
}

.quickArtistsWrapper {
  position: relative;
  padding-bottom: @vw60;
  // margin-top: @vw85;
  margin-top: @vw20;
  z-index: 3;
  width: 100vw;
  > .tinyTitle {
    text-align:center;
    margin-bottom: @vw30;
    i {
      color: @secondaryColorLight;
    }
  }
}
.marqueeWrapper {
  .transform(translateY(@vw40));
  opacity: 0;
}
.marquee {
    white-space: nowrap;
    &:hover {
      .item {
        opacity: .4;
        &:hover {
          opacity: 1;
          color: @hardWhite;
          border-color: @hardWhite;
          .artistMarqueeImage {
            img {
              .transform(translate(-50%,-50%) scale(1.1));
            }
          }
          .artistMarqueeInfo {
            .arrows {
              color: @hardWhite;
              i {
                &:first-child {
                  .transform(translate(-50%, -50%));
                }
                &:last-child {
                  .transform(translate(50%, -150%) scale(.5));
                }
              }
            }
          }
        }
      }
    }
    .itemsContainer {
        display: inline-block;
        will-change: transform;
        .item {
            display: inline-block;
            height: @vw106;
            position: relative;
            overflow: hidden;
            cursor: pointer;
            text-decoration: none;
            color: @secondaryColorLight;
            border: 1px solid @secondaryColorLight;
            .rounded(@vw6);
            width: (@vw106 * 3) + (@vw16 * 2);
            margin: 0 @vw20;
            -webkit-transition: color .3s, border-color .3s, opacity .3s;
            transition: color .3s, border-color .3s, opacity .3s;
            &:before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background: rgba(0,0,0,.1);
              backdrop-filter: blur(30px);
              -webkit-backdrop-filter: blur(30px);
            }
            * {
              cursor: pointer;
            }
            span {
              display: inline-block;
              vertical-align: top;
            }
            .artistMarqueeImage {
                position: relative;
                pointer-events: none;
                display: inline-block;
                overflow: hidden;
                width: @vw106;
                height: @vw106;
                img {
                  position: absolute;
                  top: 50%;
                  height: 100%;
                  width: 100%;
                  left: 50%;
                  .transform(translate(-50%,-50%) scale(1));
                  object-fit: cover;
                  object-position: top;
                  .transitionMore(transform, .3s);
                }
            }
            .artistMarqueeInfo {
              width: calc(100% ~"-" @vw106);
              padding: @vw18 @vw16;
            }
            .tinyTitle {
              display: inline-block;
              vertical-align: top;
              width: calc(100% ~ "-" @vw24);
              padding-right: @vw16;
              span {
                font-size: @vw16;
              }
            }
            .arrows {
              display: inline-block;
              vertical-align: top;
              width: @vw24;
              height: @vw18;
              font-size: @vw16;
              line-height: 1.4;
              position: relative;
              overflow: hidden;
              i {
                position: absolute;
                left: 50%;
                top: 50%;
                .transform(translate(-50%, -50%));
                .transitionMore(transform, .6s, 0s, cubic-bezier(0.85, 0, 0.15, 1));
                &:first-child {
                  .transform(translate(-150%, 50%) scale(.5));
                }
              }
            }
        }
    }
}

@keyframes glowAnimation {
  0% {
    transform: translate(0, 0) scale(1);
  }
  25% {
    transform: translate(2%, -2%) scale(1.02);
  }
  50% {
    transform: translate(-2%, 2%) scale(1.03);
  }
  75% {
    transform: translate(1%, -1%) scale(1.01);
  }
  100% {
    transform: translate(-1%, 1%) scale(1);
  }
}

@media all and (max-width: 1160px) {
  .homeHeaderBlock {
    padding-top: @vw100-1160 + @vw60-1160 !important;
    padding-bottom: 0 !important;
    .projectSlider {
      width: (@vw112-1160 * 4) + (@vw16-1160 * 3);
      .imageSlider {
        margin-bottom: @vw24-1160;
        height: (@vw100-1160 * 5) + @vw30-1160;
      }
      .innerCol {
        .arrowButton {
          &:not(:last-child) {
            margin-right: @vw16-1160;
          }
        }
      }
    }
    .backgroundWrapper {
      left: @vw100-1160;
    }
    .titleWrapper {
      .buttonWrapper {
        width: (@vw112-1160 * 3) + (@vw16-1160 * 2);
        margin-right: @vw16-1160;
        .transform(translateY(@vw20-1160) skewX(10deg));
      }
    }
    .buttons {
      margin-top: @vw50-1160;
      .button:not(:last-child) {
        margin-right: @vw20-1160;
      }
    }
    .imageWrapper {
      &.invisible {
        -webkit-filter: blur(@vw50-1160);
      }
    }
  }
  
}

@media all and (max-width: 580px) {
  .homeHeaderBlock {
    padding-top: @vw100-580 + @vw40-580 !important;
    .projectSlider {
      width: calc(100% ~"-" @vw44-580);
      .imageSlider {
        margin-bottom: @vw24-580;
        height: (@vw100-580 * 8) + @vw30-580;
        .slide {
          &:after {
            opacity: .6;
          }
        }
      }
      .innerCol {
        .arrowButton {
          &:not(:last-child) {
            margin-right: @vw16-580;
          }
        }
      }
    }
    .backgroundWrapper {
      left: @vw100-580;
    }
    p {
      font-size: @vw16-580;
    }
    .titleWrapper {
      top: 65%;
      .buttonWrapper {
        width: 80%;
        margin-right: @vw16-580;
        .transform(translateY(@vw20-580) skewX(10deg));
      }
    }
    .buttons {
      margin-top: @vw50-580;
      .button:not(:last-child) {
        margin-right: @vw20-580;
      }
    }
    .imageWrapper {
      &.invisible {
        -webkit-filter: blur(@vw50-580);
      }
    }
  }
}
